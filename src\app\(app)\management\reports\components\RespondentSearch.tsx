import { useState } from "react";
import { 
  VStack, 
  Text, 
  Input, 
  Box, 
  Flex,
  Spinner,
  Alert
} from "@chakra-ui/react";
import { useGetRespondentsBySearch } from "@/hook/reports/useGetRespondentsBySearch";
import { HierarchicalCheckboxCard } from "./HierarchicalCheckboxCard";

interface RespondentSearchProps {
  selectedRespondent: string | null;
  onRespondentChange: (respondentId: string | null) => void;
}

export function RespondentSearch({
  selectedRespondent,
  onRespondentChange,
}: RespondentSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  
  const { data: respondentsData, isLoading } = useGetRespondentsBySearch(
    searchTerm.trim() || undefined
  );

  const handleRespondentSelect = (respondentId: string, checked: boolean) => {
    if (checked) {
      onRespondentChange(respondentId);
    } else {
      onRespondentChange(null);
    }
  };

  return (
    <Flex
      flex={1}
      gap={6}
      alignItems="center"
      flexDirection="column"
      overflowX="auto"
    >
      <Text fontSize={{ base: "20px", lg: "30px" }} color="white" alignSelf="center">
        Buscar Respondente
      </Text>

      <VStack align="stretch" gap={4} w="100%" maxW="600px">
        <Input
          placeholder="Digite o nome do respondente..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          bg="gray.700"
          border="1px solid"
          borderColor="gray.600"
          color="white"
          _placeholder={{ color: "gray.400" }}
          _focus={{
            borderColor: "blue.400",
            boxShadow: "0 0 0 1px #3182ce",
          }}
        />

        {isLoading && (
          <Flex justify="center" py={4}>
            <Spinner size="md" color="white" />
          </Flex>
        )}

        {!searchTerm.trim() && !isLoading && (
          <Alert.Root status="info" variant="subtle">
            <Alert.Indicator />
            <Alert.Title>Digite um termo de busca</Alert.Title>
            <Alert.Description>
              Digite o nome do respondente para iniciar a busca.
            </Alert.Description>
          </Alert.Root>
        )}

        {searchTerm.trim() && !isLoading && respondentsData && respondentsData.length === 0 && (
          <Alert.Root status="warning" variant="subtle">
            <Alert.Indicator />
            <Alert.Title>Nenhum respondente encontrado</Alert.Title>
            <Alert.Description>
              Não foram encontrados respondentes com o termo "{searchTerm}".
            </Alert.Description>
          </Alert.Root>
        )}

        {respondentsData && respondentsData.length > 0 && (
          <VStack align="stretch" gap={3} maxH="400px" overflowY="auto">
            {respondentsData.map((respondent) => (
              <Box key={respondent.secureId} w="100%">
                <HierarchicalCheckboxCard
                  id={respondent.secureId}
                  label={respondent.name}
                  isChecked={selectedRespondent === respondent.secureId}
                  onChange={(_, checked) =>
                    handleRespondentSelect(respondent.secureId, checked)
                  }
                  level="segment"
                />
              </Box>
            ))}
          </VStack>
        )}
      </VStack>
    </Flex>
  );
}
