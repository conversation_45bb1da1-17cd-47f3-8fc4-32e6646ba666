import {
  HStack,
  Image,
  Input,
  InputGroup,
  Text,
  Button,
  Stack,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";

interface ReportsHeaderProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onGenerateReport: () => void;
  isGenerateDisabled: boolean;
  isGenerating: boolean;
}

export function ReportsHeader({
  searchTerm,
  onSearchChange,
  onGenerateReport,
  isGenerateDisabled,
  isGenerating,
}: ReportsHeaderProps) {
  return (
    <Stack
      w="100%"
      justifyContent={"space-between"}
      gap={8}
      mb={8}
      flexDirection={{ base: "column", lg: "row" }}
    >
      <HStack gap={{ base: 6, lg: 32 }}>
        <Image
          src="/images/logoBancoABC.svg"
          alt="Banco ABC"
          w={{ base: "70px", lg: "100px" }}
          h="auto"
        />
        <Text fontSize={{ base: "25px", lg: "40px" }}>Filtro Visão Banco</Text>
      </HStack>
      <Stack
        gap={4}
        w="50%"
        flex={1}
        flexDirection={{ base: "column", lg: "row" }}
        align={"center"}
        justifyContent={{ base: "flex-start", lg: "center" }}
      >
        <Button
          onClick={onGenerateReport}
          disabled={isGenerateDisabled}
          loading={isGenerating}
          bg="#a6864a"
          color="white"
          maxW="400px"
          _hover={{
            bg: "#8b6f3d",
          }}
          _disabled={{
            bg: "gray.400",
            cursor: "not-allowed",
          }}
          size="lg"
        >
          Gerar Relatório
        </Button>

        <InputGroup startElement={<LuSearch />} maxW="400px">
          <Input
            placeholder="Buscar por nome do respondente"
            bg="white"
            border="1px solid"
            borderColor="gray.600"
            color="black"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            _placeholder={{ color: "gray.400" }}
            _focus={{
              borderColor: "blue.400",
              boxShadow: "0 0 0 1px #3182ce",
            }}
          />
        </InputGroup>
      </Stack>
    </Stack>
  );
}
