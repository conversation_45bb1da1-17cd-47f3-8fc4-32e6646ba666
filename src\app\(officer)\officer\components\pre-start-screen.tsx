import Button from "@/components/global/buttons/button";
import { 
  Box, 
  Flex, 
  HStack, 
  Image, 
  Text, 
  VStack,
  useBreakpointValue,
} from "@chakra-ui/react";

type PreStartScreenProps = {
  onButtonClick?: () => void;
};

export default function PreStartScreen({ onButtonClick }: PreStartScreenProps) {
  const isMobile = useBreakpointValue({ base: true, lg: false})

  return (
    <>
    {!isMobile ? (
      <>
        <Flex
          direction={{ base: "column", lg: "row" }}
          flex={1}
          h={{ base: "auto", lg: "100%" }}
          minH="100vh"
          justifyContent="center"
          alignItems="center"
          bgColor={"rgb(36,35,35)"}
          gap={0}
          position={"relative"}
          overflowY="auto"
          overflowX={"hidden"}
        >
          <Image
            src="/images/logoBancoABC.svg"
            alt="Banco ABC"
            w={"100px"}
            h="auto"
            position={"absolute"}
            top={{
              "2xl": 20,
              base: 10,
            }}
            left={{ "2xl": 20, base: 6, lg: 10 }}
          />
          <VStack
            mt={{ base: 20, lg: 0 }}
            w={{ base: "100%", lg: "50%" }}
            h="auto"
            justify={"center"}
            px={{ base: 6, md: 10, lg: 20 }}
            pt={{ base: 24, lg: 15 }}
            pb={{ base: 10, lg: 15 }}
          >
            <VStack gap={{ "2xl": 10, base: 5 }} alignItems="start">
              <Text
                fontSize={{ "2xl": 32, base: 24 }}
                fontWeight="light"
                color={"white"}
              >
                Aqui, você encontra um verdadeiro mapa estratégico para o seu
                crescimento profissional.
              </Text>
              <Text
                fontSize={{ "2xl": 24, base: 16 }}
                fontWeight="medium"
                color={"white"}
              >
                Você está prestes a iniciar um assessment cuidadosamente projetado
                para mapear suas competências e impulsionar seu crescimento. Serão
                30 questões situacionais, desenvolvidas para refletir os desafios
                reais do seu dia a dia como Gerente de Relacionamento.
              </Text>
              <Text
                fontSize={{ "2xl": 24, base: 16 }}
                fontWeight="medium"
                color={"white"}
              >
                Reserve cerca de 1 hora para responder às perguntas com calma e
                atenção. Comece agora sua jornada de evolução.
              </Text>
              <Button p={6} px={8} borderRadius={20} onClick={onButtonClick}>
                <Text fontSize={{ "2xl": 24, base: 20 }} fontWeight={"bold"}>
                  Começar
                </Text>
              </Button>
            </VStack>
          </VStack>
          <VStack
            w={{ base: "100%", lg: "50%" }}
            h="auto"
            justify={"center"}
            align={"center"}
            gap={8}
            pt={{ base: 0, lg: 15 }}
            pb={{ base: 10, lg: 15 }}
            px={{ base: 6, md: 10, "2xl": 0 }}
          >
            <Text
              fontSize={{ "2xl": 32, base: 24 }}
              fontWeight="light"
              color={"white"}
            >
              O que você pode esperar ao final?
            </Text>
            <Flex
              direction={{ base: "column", md: "row" }}
              w={"100%"}
              justifyContent="space-around"
              px={{
                "2xl": 20,
                base: 0,
              }}
              gap={5}
            >
              <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
                <Box position="relative" w="100%">
                  <Image
                    src="/images/feedback-img.png"
                    alt="Banco ABC"
                    w="100%"
                    h="auto"
                    borderRadius={20}
                  />
                  <Box
                    position="absolute"
                    bottom="0"
                    left={{
                      base: 14,
                      "2xl": 16,
                    }}
                    w="100%"
                    p={{
                      base: 3,
                      "2xl": 5,
                    }}
                    borderBottomRadius={20}
                  >
                    <Text
                      color="white"
                      fontSize={{ base: "lg", "2xl": "2xl" }}
                      fontWeight="bold"
                    >
                      Feedback Personalizado
                    </Text>
                  </Box>
                </Box>
                <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
                  Descubra seus pontos fortes e identifique áreas de desenvolvimento
                  com clareza.
                </Text>
              </VStack>
              <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
                <Box position="relative" w="100%">
                  <Image
                    src="/images/feedforward-img.png"
                    alt="Banco ABC"
                    w="100%"
                    h="auto"
                    borderRadius={20}
                  />
                  <Box
                    position="absolute"
                    bottom="0"
                    left={{
                      base: 14,
                      "2xl": 16,
                    }}
                    w="100%"
                    p={{
                      base: 3,
                      "2xl": 5,
                    }}
                    borderBottomRadius={20}
                  >
                    <Text
                      color="white"
                      fontSize={{ base: "lg", "2xl": "2xl" }}
                      fontWeight="bold"
                    >
                      Feedforward Estratégico
                    </Text>
                  </Box>
                </Box>
                <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
                  Receba recomendações práticas de aprendizado, conectadas ao
                  portfólio da Escola do ABC BRASIL.
                </Text>
              </VStack>
            </Flex>
          </VStack>
        </Flex>
      </>
    ):(
      <>
        <Flex
        direction={"column"}
        flex={1}
        h={"auto"}
        minH="100vh"
        justifyContent="center"
        alignItems="center"
        bgColor={"rgb(36,35,35)"}
        gap={0}
        position={"relative"}
        overflowY="auto"
        overflowX={"hidden"}
      >
        <Image
          src="/images/logoBancoABC.svg"
          alt="Banco ABC"
          w={"100px"}
          h="auto"
          position={"absolute"}
          top={10}
          left={6}
        />
        <VStack
          mt={20}
          w={"100%"}
          h="auto"
          justify={"center"}
          px={6}
          pt={24}
          pb={10}
        >
          <VStack gap={{ "2xl": 10, base: 5 }} alignItems="start">
            <Text
              fontSize={{ "2xl": 32, base: 24 }}
              fontWeight="light"
              color={"white"}
            >
              Aqui, você encontra um verdadeiro mapa estratégico para o seu
              crescimento profissional.
            </Text>
            <Text
              fontSize={{ "2xl": 24, base: 16 }}
              fontWeight="medium"
              color={"white"}
            >
              Você está prestes a iniciar um assessment cuidadosamente projetado
              para mapear suas competências e impulsionar seu crescimento. Serão
              30 questões situacionais, desenvolvidas para refletir os desafios
              reais do seu dia a dia como Gerente de Relacionamento.
            </Text>
          </VStack>
        </VStack>
        <VStack
          w={{ base: "100%", lg: "50%" }}
          h="auto"
          justify={"center"}
          align={"center"}
          gap={8}
          pt={{ base: 0, lg: 15 }}
          pb={{ base: 10, lg: 15 }}
          px={{ base: 6, md: 10, "2xl": 0 }}
        >
          <Text
            fontSize={{ "2xl": 32, base: 24 }}
            fontWeight="light"
            color={"white"}
          >
            O que você pode esperar ao final?
          </Text>
          <Flex
            direction={{ base: "column", md: "row" }}
            w={"100%"}
            justifyContent="space-around"
            px={{
              "2xl": 20,
              base: 0,
            }}
            gap={5}
          >
            <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
              <Box position="relative" w="100%">
                <Image
                  src="/images/feedback-img.png"
                  alt="Banco ABC"
                  w="100%"
                  h="auto"
                  borderRadius={20}
                />
                <Box
                  position="absolute"
                  bottom="0"
                  left={{
                    base: 14,
                    "2xl": 16,
                  }}
                  w="100%"
                  p={{
                    base: 3,
                    "2xl": 5,
                  }}
                  borderBottomRadius={20}
                >
                  <Text
                    color="white"
                    fontSize={{ base: "lg", "2xl": "2xl" }}
                    fontWeight="bold"
                  >
                    Feedback Personalizado
                  </Text>
                </Box>
              </Box>
              <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
                Descubra seus pontos fortes e identifique áreas de desenvolvimento
                com clareza.
              </Text>
            </VStack>
            <VStack w={{ base: "100%", md: "50%" }} alignItems="start" gap={2}>
              <Box position="relative" w="100%">
                <Image
                  src="/images/feedforward-img.png"
                  alt="Banco ABC"
                  w="100%"
                  h="auto"
                  borderRadius={20}
                />
                <Box
                  position="absolute"
                  bottom="0"
                  left={{
                    base: 14,
                    "2xl": 16,
                  }}
                  w="100%"
                  p={{
                    base: 3,
                    "2xl": 5,
                  }}
                  borderBottomRadius={20}
                >
                  <Text
                    color="white"
                    fontSize={{ base: "lg", "2xl": "2xl" }}
                    fontWeight="bold"
                  >
                    Feedforward Estratégico
                  </Text>
                </Box>
              </Box>

              <VStack gap={5} alignItems={"start"}>
                <Text color="white" fontSize={{ base: "sm", "2xl": "md" }}>
                  Receba recomendações práticas de aprendizado, conectadas ao
                  portfólio da Escola do ABC BRASIL.
                </Text>
                <Text
                  fontSize={{ "2xl": 24, base: 16 }}
                  fontWeight="medium"
                  color={"white"}
                >
                  Reserve cerca de 1 hora para responder às perguntas com calma e
                  atenção. Comece agora sua jornada de evolução.
                </Text>
                <Button p={6} px={8} borderRadius={20} onClick={onButtonClick}>
                  <Text fontSize={{ "2xl": 24, base: 20 }} fontWeight={"bold"}>
                    Começar
                  </Text>
                </Button>
              </VStack>
            </VStack>
          </Flex>
        </VStack>
      </Flex>
      </>
    )}
    </>
  );
}
