import { api } from "@/services/api";
import { useMutation } from "@tanstack/react-query";
import {
  RespondentReportGenerationRequest,
  ReportGenerationResponse
} from "@/app/(app)/management/reports/data/types";
import { toaster } from "@/components/ui/toaster";
import { AxiosError } from "axios";
import { ApiErrorInputDTO } from "@/utils/types/DTO/api-error.dto";

async function generateRespondentReport(data: RespondentReportGenerationRequest): Promise<ReportGenerationResponse> {
  const response = await api.post<ReportGenerationResponse>("/management/report/respondent", data);
  return response.data;
}

export function useGenerateRespondentReport() {
  return useMutation({
    mutationFn: generateRespondentReport,
    onSuccess: () => {
      toaster.success({
        title: "Relatório do respondente gerado com sucesso!",
        description: "Os dados do relatório foram processados.",
      });
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao gerar relatório do respondente",
        description: error?.response?.data?.message || "Erro inesperado ao gerar o relatório",
      });
    },
  });
}
