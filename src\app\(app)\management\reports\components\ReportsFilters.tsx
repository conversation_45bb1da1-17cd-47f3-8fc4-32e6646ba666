import { Checkbox, CheckboxCard, VStack, Wrap, WrapItem } from "@chakra-ui/react";

interface ReportsFiltersProps {
  allSelected: boolean;
  selectedRounds: Set<string>;
  onAllChange: (checked: boolean) => void;
  onRoundChange: (round: string, checked: boolean) => void;
}

export function ReportsFilters({
  allSelected,
  selectedRounds,
  onAllChange,
  onRoundChange,
}: ReportsFiltersProps) {
  return (
    <VStack gap={4} align="stretch" minW="full" >
      <CheckboxCard.Root
        borderRadius="lg"
        bg={"white"}
        align="center"
        w={"fit-content"}
        border="none"
      >
        <CheckboxCard.HiddenInput
          checked={allSelected}
          onChange={(e) => onAllChange(e.target.checked)}
        />
        <CheckboxCard.Control p={0} pr={2}>
          <CheckboxCard.Indicator
            w={10}
            h="auto"
            bgColor="#a6864a"
            borderLeftRadius="lg"
            borderColor="gray.600"
          />
          <CheckboxCard.Label
            color="black"
            fontWeight="bold"
            fontSize="sm"
          >
            TODOS
          </CheckboxCard.Label>
        </CheckboxCard.Control>
      </CheckboxCard.Root>

      <Wrap gap={4} justify="flex-start" flexDirection={{ base: "column", lg: "row"}}>
        <WrapItem>
          <Checkbox.Root
            variant={"subtle"}
            checked={selectedRounds.has("rodada")}
            onCheckedChange={(checked) =>
              onRoundChange("rodada", !!checked.checked)
            }
          >
            <Checkbox.HiddenInput />
            <Checkbox.Control
              w={8}
              h={8}
              borderRadius="2xl"
              color={"transparent"}
              bgColor={
                selectedRounds.has("rodada") ? "#a6864a" : "gray.600"
              }
            />
            <Checkbox.Label fontSize="xl">Rodada</Checkbox.Label>
          </Checkbox.Root>
        </WrapItem>

        <WrapItem>
          <Checkbox.Root
            variant={"subtle"}
            checked={selectedRounds.has("rodada1")}
            onCheckedChange={(checked) =>
              onRoundChange("rodada1", !!checked.checked)
            }
          >
            <Checkbox.HiddenInput />
            <Checkbox.Control
              w={8}
              h={8}
              borderRadius="2xl"
              color={"transparent"}
              bgColor={
                selectedRounds.has("rodada1") ? "#a6864a" : "gray.600"
              }
            />
            <Checkbox.Label fontSize="xl">Rodada 1</Checkbox.Label>
          </Checkbox.Root>
        </WrapItem>

        <WrapItem>
          <Checkbox.Root
            variant={"subtle"}
            checked={selectedRounds.has("rodada2")}
            onCheckedChange={(checked) =>
              onRoundChange("rodada2", !!checked.checked)
            }
          >
            <Checkbox.HiddenInput />
            <Checkbox.Control
              w={8}
              h={8}
              borderRadius="2xl"
              color={"transparent"}
              bgColor={
                selectedRounds.has("rodada2") ? "#a6864a" : "gray.600"
              }
            />
            <Checkbox.Label fontSize="xl">Rodada 2</Checkbox.Label>
          </Checkbox.Root>
        </WrapItem>
      </Wrap>
    </VStack>
  );
}
