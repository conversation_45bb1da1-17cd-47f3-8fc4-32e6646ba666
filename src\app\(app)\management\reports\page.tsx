"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spin<PERSON>, Text, VStack } from "@chakra-ui/react";
import { useHierarchicalSelection } from "./hooks/useHierarchicalSelection";
import { useRoundSelection } from "./hooks/useRoundSelection";
import { ReportsHeader } from "./components/ReportsHeader";
import { ReportsFilters } from "./components/ReportsFilters";
import { SegmentDisplay } from "./components/SegmentDisplay";
import { ReportResults } from "./components/ReportResults";
import { useState } from "react";
import { useGetHierarchyReports } from "@/hook/reports/useGetHierarchyReports";
import { useGenerateReport } from "@/hook/reports/useGenerateReport";
import {
  validateReportSelections,
  createReportRequest,
} from "./utils/reportDataCollector";
import { ReportGenerationResponse } from "./data/types";
import { toaster } from "@/components/ui/toaster";
import { useGetRespondentsBySearch } from "@/hook/reports/useGetRespondentsBySearch";
import { useGenerateRespondentReport } from "@/hook/reports/useGenerateRespondentReport";
import { SearchMode } from "./data/types";
import { SearchModeSelector } from "./components/SearchModeSelector";
import { RespondentSearch } from "./components/RespondentSearch";

export default function Reports() {
  const [searchTerm, setSearchTerm] = useState("");
  const [reportData, setReportData] = useState<ReportGenerationResponse | null>(
    null
  );
  const [showResults, setShowResults] = useState(false);
  const [searchMode, setSearchMode] = useState<SearchMode>("segments");
  const [selectedRespondent, setSelectedRespondent] = useState<string | null>(
    null
  );

  const { data: hierarchyReportsData, isLoading } = useGetHierarchyReports();
  const { data: respondentsData } = useGetRespondentsBySearch(searchTerm);
  console.log("respondentsData: ", respondentsData);

  const {
    selectedItems,
    allSelected,
    handleItemChangeByPath,
    handleAllChange,
    isItemSelectedByPath,
  } = useHierarchicalSelection(hierarchyReportsData?.data || []);
  const { selectedRounds, handleRoundChange } = useRoundSelection();

  const generateReportMutation = useGenerateReport();
  const generateRespondentReportMutation = useGenerateRespondentReport();

  // Validation for Generate Report button
  const isGenerateDisabled =
    selectedRounds.size === 0 ||
    (searchMode === "segments" &&
      (!hierarchyReportsData?.data || selectedItems.size === 0)) ||
    (searchMode === "respondent" && !selectedRespondent);

  // Handle report generation
  const handleGenerateReport = async () => {
    if (searchMode === "segments") {
      if (!hierarchyReportsData?.data) {
        toaster.error({
          title: "Erro",
          description: "Dados da hierarquia não carregados",
        });
        return;
      }

      // Validate selections
      const validation = validateReportSelections(
        selectedRounds,
        selectedItems,
        hierarchyReportsData.data
      );

      if (!validation.isValid) {
        toaster.error({
          title: "Seleção inválida",
          description: validation.message,
        });
        return;
      }

      try {
        // Create request payload
        const requestData = createReportRequest(
          selectedRounds,
          selectedItems,
          hierarchyReportsData.data
        );

        // Generate report
        const response = await generateReportMutation.mutateAsync(requestData);
        setReportData(response);
        setShowResults(true);
      } catch (error) {
        console.error("Error generating report:", error);
      }
    } else if (searchMode === "respondent") {
      if (!selectedRespondent) {
        toaster.error({
          title: "Respondente não selecionado",
          description: "Selecione um respondente para gerar o relatório.",
        });
        return;
      }

      try {
        // Convert rounds format
        const rounds =
          selectedRounds.has("rodada1") && selectedRounds.has("rodada2")
            ? "both"
            : selectedRounds.has("rodada1")
            ? "checkIn"
            : "checkOut";

        const requestData = {
          rounds: rounds as "checkIn" | "checkOut" | "both",
          respondentSecureId: selectedRespondent,
        };

        // Generate respondent report
        const response = await generateRespondentReportMutation.mutateAsync(
          requestData
        );
        setReportData(response);
        setShowResults(true);
      } catch (error) {
        console.error("Error generating respondent report:", error);
      }
    }
  };

  if (isLoading || !hierarchyReportsData) {
    return (
      <Flex flex={1} justify="center" align="center">
        <Spinner size="xl" />
      </Flex>
    );
  }

  return (
    <Flex flex={1} position="relative" mt={10} overflowX="auto">
      <VStack w="100%" gap={6} align="stretch">
        <VStack p={6} align="stretch">
          {/* Header */}
          <ReportsHeader
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onGenerateReport={handleGenerateReport}
            isGenerateDisabled={isGenerateDisabled}
            isGenerating={generateReportMutation.isPending}
          />

          {/* Main Content */}
          <HStack flexDirection={"column"} align="flex-start" gap={8}>
            {/* Search Mode Selector */}
            <SearchModeSelector
              selectedMode={searchMode}
              onModeChange={(mode) => {
                setSearchMode(mode);
                setSelectedRespondent(null);
                setShowResults(false);
                setReportData(null);
              }}
            />

            {/* Controls */}
            <ReportsFilters
              allSelected={searchMode === "segments" ? allSelected : false}
              selectedRounds={selectedRounds}
              onAllChange={
                searchMode === "segments" ? handleAllChange : () => {}
              }
              onRoundChange={handleRoundChange}
              hideAllOption={searchMode === "respondent"}
            />

            {/* Conditional Content Based on Search Mode */}
            {searchMode === "segments" ? (
              <SegmentDisplay
                segments={hierarchyReportsData?.data}
                onItemChangeByPath={handleItemChangeByPath}
                isItemSelectedByPath={isItemSelectedByPath}
              />
            ) : (
              <RespondentSearch
                selectedRespondent={selectedRespondent}
                onRespondentChange={setSelectedRespondent}
              />
            )}
          </HStack>
        </VStack>

        {/* Report Results - Only shown after successful report generation */}
        {showResults && reportData ? (
          <ReportResults reportData={reportData} searchMode={searchMode} />
        ) : null}
      </VStack>
    </Flex>
  );
}
