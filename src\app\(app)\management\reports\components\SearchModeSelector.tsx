import { VStack, Text } from "@chakra-ui/react";
import FormCombobox from "@/components/global/combobox/form-combobox";
import { SearchMode, SearchModeOption } from "../data/types";

interface SearchModeSelectorProps {
  selectedMode: SearchMode;
  onModeChange: (mode: SearchMode) => void;
}

const searchModeOptions: SearchModeOption[] = [
  { value: "respondent", label: "Respondente" },
  { value: "segments", label: "Segmentos/Cargos" },
];

export function SearchModeSelector({
  selectedMode,
  onModeChange,
}: SearchModeSelectorProps) {
  return (
    <VStack align="stretch" gap={4} w="300px">
      <Text fontSize="lg" color="white" fontWeight="medium">
        Modo de Busca
      </Text>
      <FormCombobox
        options={searchModeOptions}
        value={selectedMode}
        onValueChange={(value) => onModeChange(value as SearchMode)}
        placeholder="Selecione o modo de busca"
        label=""
      />
    </VStack>
  );
}
