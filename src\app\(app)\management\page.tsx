"use client";
import {
  Box,
  Flex,
  Grid,
  GridItem,
  HStack,
  Image,
  Text,
  VStack,
  useBreakpointValue
} from "@chakra-ui/react";

export default function Management() {
  const isMobile = useBreakpointValue({ base: true, lg: false });

  return (
    <>
    {isMobile ? (
      <>
        <Flex flex={1} position={"relative"} overflow={"hidden"} mt={10}>
          <Box
            position="absolute"
            bottom={0}
            top={10}
            left={"15rem"}
            w={"100%"}
            h={"100vh"} 
            transform={"scale(1.5)"}
            bgImg="url(/images/login/padraoBG.svg)"
            bgRepeat="no-repeat"
            bgPos="center left"
            bgSize="contain"
            zIndex={0}
          />
          <Grid
            templateColumns={"repeat(1, 2fr)"}
            gap={1}
            w={"100vw"}
            h="100vh"
            position={"relative"}
          >
            <GridItem>
              <VStack height="100vw" p={8} gap={8} alignItems="center">
                <HStack
                  w={"100vw"}
                  justifyContent="start"
                  alignItems="center"
                  gap={2}

                >
                  <Image
                    src="/images/logoBancoABC.svg"
                    alt="Banco ABC"
                    w={"40px"}
                    h="auto"
                  />
                  <Text fontSize={"md"}>
                    Dashboard Assessment ABC BRASIL
                  </Text>
                </HStack>
                <Flex
                  as="main"
                  w="100%"
                  ml={0}
                  flex={1}
                  alignItems="center"
                  justifyContent="start"
                >
                  <VStack alignItems="center">
                    <Text
                      fontSize="xl"
                      letterSpacing={"wider"}
                      alignSelf={"start"}
                      color="#a6864a"
                    >
                      INTRODUÇÃO
                    </Text>
                    <Text fontSize={{base: "xs", lg: "md"}} textAlign="start" maxW="270px">
                      Aqui você encontra sua proficiência nas 36 competências essenciais para um gerente de relacionamento do ABC Brasil, com base em aspectos técnicos, estratégicos e relacionais.
                    </Text>
                    <Text fontSize={{base: "xs", lg: "md"}} textAlign="start" maxW="270px">
                      O objetivo é oferecer clareza sobre seus pontos fortes e oportunidades de desenvolvimento, apoiando uma trilha de aprendizado personalizada.
                    </Text>
                    <Text fontSize={{base: "xs", lg: "md"}} textAlign="start" maxW="270px">
                      A escala vai de 1 a 5, do nível Iniciante ao Excepcional.
                      Você está no centro da nossa estratégia de excelência.
                      Boa leitura. 
                    </Text>
                  </VStack>
                </Flex>
              </VStack>
            </GridItem>
            <GridItem>
              <Box
                // hideBelow={"xl"}
                bgImage="url(/images/login/bg-02.png)"
                w="100vw"
                h="100vh"
                rounded={"xl"}
                bgRepeat="no-repeat"
                bgSize="100% auto"
                position="relative"
                _after={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background:
                    "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
                }}
              />
            </GridItem>
          </Grid>
        </Flex>
      </>
    ) : (
      <>
        <Flex flex={1} position={"relative"} overflow={"hidden"}>
          <Box
            position="absolute"
            top={0}
            left={0}
            transform="translateY(50%)"
            w={"100%"}
            h={"100%"}
            bgImg="url(/images/login/padraoBG.svg)"
            bgRepeat="no-repeat"
            bgPos="center bottom"
            bgSize="contain"
            clipPath="inset(0 0 50% 0)"
          />
          <Grid
            templateColumns="repeat(2, 1fr)"
            gap={1}
            w={"100vw"}
            h="100vh"
            position={"relative"}
          >
            <GridItem>
              <VStack height="100vh" p={8} gap={8} alignItems="center">
                <HStack
                  w={"100%"}
                  justifyContent="center"
                  alignItems="center"
                  gap={8}
                >
                  <Image
                    src="/images/logoBancoABC.svg"
                    alt="Banco ABC"
                    w={"100px"}
                    h="auto"
                  />
                  <Text fontSize={"40px"}>
                    Dashboard Assessment ABC BRASIL
                  </Text>
                </HStack>
                <Flex
                  as="main"
                  w="100%"
                  flex={1}
                  alignItems="center"
                  justifyContent="center"
                >
                  <VStack alignItems="center">
                    <Text
                      fontSize="32px"
                      letterSpacing={"wider"}
                      alignSelf={"start"}
                      color="#a6864a"
                    >
                      INTRODUÇÃO
                    </Text>
                    <Text fontSize="md" textAlign="start" maxW="300px">
                      Aqui você encontra sua proficiência nas 36 competências essenciais para um gerente de relacionamento do ABC Brasil, com base em aspectos técnicos, estratégicos e relacionais.
                    </Text>
                    <Text fontSize="md" textAlign="start" maxW="300px">
                      O objetivo é oferecer clareza sobre seus pontos fortes e oportunidades de desenvolvimento, apoiando uma trilha de aprendizado personalizada.
                    </Text>
                    <Text fontSize="md" textAlign="start" maxW="300px">
                      A escala vai de 1 a 5, do nível Iniciante ao Excepcional.
                    </Text>
                    <Text fontSize="md" textAlign="start" maxW="300px">
                      Você está no centro da nossa estratégia de excelência.
                      Boa leitura. 
                    </Text>
                  </VStack>
                </Flex>
              </VStack>
            </GridItem>
            <GridItem>
              <Box
                // hideBelow={"xl"}
                bgImage="url(/images/login/bg-02.png)"
                w="100vh"
                h="100vh"
                bgRepeat="no-repeat"
                bgSize="cover"
                position="relative"
                _after={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background:
                    "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
                }}
              />
            </GridItem>
          </Grid>
        </Flex>
      </>
    )}
    </>
    
  );
}
