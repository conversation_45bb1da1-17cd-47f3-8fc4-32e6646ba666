import { api } from "@/services/api";
import { GetRespondentsBySearchDto } from "@/utils/types/DTO/respondents/respondents.dto";
import { useQuery } from "@tanstack/react-query";

async function getRespondentsBySearch(searchTerm?: string) {
  const { data } = await api.get<GetRespondentsBySearchDto>(`/management/respondents`, {
    params: {
      searchName: searchTerm,
    },
  });
  return data;
}

export function useGetRespondentsBySearch(searchTerm?: string) {
  console.log("searchTerm: ", searchTerm);
  return useQuery({
    queryKey: ["respondents", searchTerm],
    queryFn: async () => await getRespondentsBySearch(searchTerm),
  });
}